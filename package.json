{"name": "gmo-saas-auction-system", "version": "1.0.0", "description": "GMOのSaas入札システム", "main": "index.js", "scripts": {"tf-lambda": "cd ./infrastructure/deploy-scripts && node ./lambda.cjs", "tf-gateway": "cd ./infrastructure/deploy-scripts && node ./gateway.cjs", "lint-fix": "eslint --quiet ./auction-side/vue.config.js --fix", "dev-admin": "cd ./admin-side && npm run dev", "dev-auction": "cd ./auction-side && npm run dev", "format": "prettier --write \"infrastructure/**/*.{js,vue}\" \"admin-side/src/**/*.{js,vue}\" \"auction-side/src/**/*.{js,vue}\"", "format:check": "prettier --check \"infrastructure/**/*.{js,vue}\" \"admin-side/src/**/*.{js,vue}\" \"auction-side/src/**/*.{js,vue}\"", "lint": "eslint --quiet  ./infrastructure/common/admin-side/gateway-resource/get-notice-list/source/index.js"}, "author": "GMO", "license": "ISC", "devDependencies": {"@eslint/js": "^9.13.0", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-vue": "^9.29.1", "globals": "^15.11.0", "prettier": "3.3.3"}, "engines": {"node": ">= 20.x", "npm": ">= 10.x"}, "dependencies": {"jquery-match-height": "^0.7.2", "slick-carousel": "^1.8.1"}}