<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardHeader>
        <strong>全国一律送料の設定</strong>
      </CCardHeader>
      <CCardBody>
        <AddNewItem @add="addNewItem" />
      </CCardBody>
    </CCard>
    <CRow>
      <CCol sm="12">
        <PostageTable
          name="resourceList"
          :items="resourceList"
          :total_count="totalCount"
          :current_count="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :itemsSorter="itemsSorter"
          caption="項目一覧"
          @page-change="pageChange"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isErrorDialog"
      @close="
        () => {
          isErrorDialog = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody closeButton>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="isErrorDialog = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {useCommonStore} from '@/store/common';
  import {computed, onMounted, ref, watch} from 'vue';
  import {useRoute, useRouter} from 'vue-router';
  import AddNewItem from '../../components/tenant/postage/AddNewItem.vue';
  import PostageTable from '../../components/tenant/postage/PostageTable.vue';

  const route = useRoute();
  const router = useRouter();
  const store = useCommonStore();

  const constantList = ref([]);
  const loading = ref(true);
  // 検索条件
  const pageOptions = ref([]);
  const languageOptions = ref([]);

  // Screen params
  const resourceList = ref([]);
  const search_condition = ref({
    page: '1',
    language_code: 'ja',
  });
  const activePage = ref(1);
  const itemsPerPage = ref(10);
  const pages = ref(1);
  const itemsSorter = ref({});

  // Counting
  const current_count = ref(0);
  const total_count = ref(0);

  // CSV用
  const validateResult = ref([]);

  // Error dialog
  const isErrorDialog = ref(false);

  onMounted(() => {
    getConstants()
      .then(() => {
        getResourceList()
          .then(postage => {
            resourceList.value = postage;

            itemsPerPage.value = store.itemsPerPage;

            pages.value =
              parseInt(resourceList.value.length / itemsPerPage.value, 10) +
              (resourceList.value.length % itemsPerPage.value > 0 ? 1 : 0);
            activePage.value =
              store.activePage > pages.value
                ? Number(pages.value)
                : store.activePage;
            router.push({query: {page: activePage.value}}).catch(() => {});
          })
          .catch(error => {
            console.log(error);
            loading.value = false;
            validateResult.value = Methods.parseHtmlResponseError(
              router,
              error
            );
          });
      })
      .catch(error => {
        console.log(error);
        Methods.parseHtmlResponseError(router, error);
      });
  });

  watch(
    () => route.query,
    query => {
      if (query?.page) {
        activePage.value = Number(query.page);
      }
    }
  );

  watch(
    () => search_condition,
    newVal => {
      store.set(['resourceSearchCondition', newVal]);
    }
  );
  watch(
    () => itemsPerPage,
    newVal => {
      if (resourceList.value.length > newVal) {
        pages.value =
          parseInt(resourceList.value.length / newVal, 10) +
          (resourceList.value.length % newVal > 0 ? 1 : 0);
      } else {
        pages.value = 1;
      }
    }
  );

  const totalCount = computed(() => {
    return Base.number2string(total_count.value);
  });

  const currentCount = computed(() => {
    return Base.number2string(current_count.value);
  });

  const getConstants = () => {
    loading.value = true;
    return Methods.apiExecute('get-constants-by-keys', {
      key_strings: ['LANGUAGE_CODE', 'INPUT_VALIDATION'],
    }).then(response => {
      if (response.status === 200) {
        constantList.value = response.data;

        pageOptions.value.push({value: 1, label: 'HOME_PAGE'});
        pageOptions.value.push({value: 2, label: '商品詳細'});
        pageOptions.value.push({value: 2, label: '入札中'});
        pageOptions.value.push({value: 2, label: 'お気に入り'});

        languageOptions.value.push({label: '', value: null});
        for (const i of response.data.filter(
          x => x.key_string === 'LANGUAGE_CODE'
        )) {
          languageOptions.value.push({label: i.value2, value: i.value1});
        }
        return Promise.resolve();
      }
      return Promise.resolve();
    });
  };

  const search = () => {
    isErrorDialog.value = false;
    if (
      search_condition.value.resource_type === null &&
      search_condition.value.start_area === null &&
      search_condition.value.end_area === null
    ) {
      validateResult.value = ['1つ以上の条件を選択してください。'];
      isErrorDialog.value = true;
      return;
    }
    getResourceList()
      .then(postage => {
        resourceList.value = postage;

        pages.value =
          parseInt(resourceList.value.length / itemsPerPage.value, 10) +
          (resourceList.value.length % itemsPerPage.value > 0 ? 1 : 0);
        sorterChange({asc: true, column: 'resource_type'});
      })
      .catch(error => {
        loading.value = false;
        Methods.parseHtmlResponseError(router, error);
      });
  };

  const getResourceList = () => {
    total_count.value = 0;
    current_count.value = 0;

    console.log('検索条件:', search_condition.value);

    // Request to server
    return Methods.apiExecute('get-postage-list', search_condition.value).then(
      response => {
        if (response.status === 200) {
          loading.value = false;
          const resourceList = response.data.data;
          total_count.value = response.data
            ? response.data.total_count || 0
            : 0;
          current_count.value = response.data
            ? response.data.current_count || 0
            : 0;
          return Promise.resolve(resourceList);
        }
        return Promise.resolve(null);
      }
    );
  };

  const pageChange = val => {
    store.set(['activePage', val]);
    router.push({query: {page: val}}).catch(() => {});
  };
  const paginationChange = val => {
    itemsPerPage.value = val;
    store.set(['itemsPerPage', val]);
  };
  const sorterChange = val => {
    itemsSorter.value = val;
    store.set(['itemsSorter', val]);
    this.pageChange(1);
  };
  const addNewItem = item => {
    console.log('addNewItem');
    resourceList.value.push({
      ...item,
      id: resourceList.value.length + 1,
    });
  };
</script>
