// Test jQuery and plugins availability
import $ from 'jquery'

export function testJQueryPlugins() {
  console.log('Testing jQuery and plugins...')
  
  // Test jQuery
  if (typeof $ === 'function') {
    console.log('✅ jQuery is loaded')
  } else {
    console.error('❌ jQuery is not loaded')
  }
  
  // Test Slick
  if (typeof $.fn.slick === 'function') {
    console.log('✅ Slick carousel is loaded')
  } else {
    console.error('❌ Slick carousel is not loaded')
  }
  
  // Test matchHeight
  if (typeof $.fn.matchHeight === 'function') {
    console.log('✅ matchHeight plugin is loaded')
  } else {
    console.error('❌ matchHeight plugin is not loaded')
  }
  
  // Test global jQuery
  if (typeof window.$ === 'function' && typeof window.jQuery === 'function') {
    console.log('✅ jQuery is available globally')
  } else {
    console.error('❌ jQuery is not available globally')
  }
}
