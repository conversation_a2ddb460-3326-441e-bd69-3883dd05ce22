// Common JavaScript functions for Vue 3 + Vite setup
import $ from 'jquery'

// Make sure jQuery is available globally
window.jQuery = $
window.$ = $

// viewport処理
var isSp = window.matchMedia('(max-width: 767px)')
$(window).on('load resize', function () {
  if (isSp.matches) {
    $("meta[name='viewport']").attr('content', 'width=device-width,initial-scale=1')
  }
})

// picture要素 IE対策
document.createElement('picture')

//ハンバーガーメニュー
$(function () {
  $('header p.btnMenu').click(function () {
    $('header .gNav').slideToggle()
    $('header p.btnMenu').toggleClass('close')
  })
})

//SPナビ 入れ子の開閉
$(function () {
  $('header .gNav nav ul.only_sp li p').click(function () {
    $(this).next('ul').slideToggle()
    $(this).toggleClass('close')
  })
})

$(function () {
  $('footer nav .fNav_sp > ul > li > p').click(function () {
    $(this).next('ul,dl').slideToggle()
    $(this).toggleClass('close')
  })
})

// 高さ合わせ - matchHeight plugin
$(function () {
  if (typeof $.fn.matchHeight === 'function') {
    $('.matchH').matchHeight()
  } else {
    console.warn('matchHeight plugin is not loaded')
  }
})

// Footer positioning function
function footer() {
  if (typeof $ !== 'undefined' && $('footer').length > 0) {
    try {
      var footerTop = $('footer').offset().top
      var windowHeight = $(window).height()
      var bodyHeight = $('body').height()

      if (bodyHeight < windowHeight) {
        $('footer').css({
          position: 'absolute',
          bottom: 0,
          width: '100%'
        })
      }
    } catch (error) {
      console.error('Error in footer positioning:', error)
    }
  }
}

// Export functions that might be needed elsewhere
export { footer }

// [TOP]おすすめ商品スライダー initialization
function initTopSlider() {
  const $slider = $('.list-item-gallery.top')

  if ($slider.length && typeof $slider.slick === 'function') {
    // Slick初期化
    $slider.slick({
      arrows: true,
      dots: true,
      infinite: true,
      slidesToShow: 5,
      slidesToScroll: 1,
      responsive: [
        {
          breakpoint: 1081,
          settings: {
            slidesToShow: 3
          }
        },
        {
          breakpoint: 767,
          settings: {
            slidesToShow: 2
          }
        }
      ]
    })

    // 高さを揃える関数
    function equalizeSlideHeights() {
      let maxHeight = 0

      $slider.find('.slick-slide').each(function () {
        $(this).css('height', 'auto') // 高さリセット
        const h = $(this).outerHeight()
        if (h > maxHeight) maxHeight = h
      })

      $slider.find('.slick-slide').css('height', maxHeight + 'px')
    }

    // 初期化直後・スライド変更時に高さを揃える
    $slider.on('setPosition', function () {
      equalizeSlideHeights()
    })
  }
}

// Initialize common functionality when DOM is ready
$(document).ready(function () {
  // Initialize footer positioning
  footer()

  // Initialize top slider if present
  setTimeout(() => {
    initTopSlider()
  }, 100)

  // Re-run footer positioning on window resize
  $(window).resize(function () {
    footer()
  })
})
