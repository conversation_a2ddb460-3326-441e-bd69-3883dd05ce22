<script setup>
// import { computed, defineAsyncComponent } from 'vue'
// import { useSearchResultStore } from '../../../stores/search-results'

// const ProductList = defineAsyncComponent(() => import('../search-list/ProductList.vue'))

// const searchStore = useSearchResultStore()

// const productList = computed(() => searchStore.productList.all)
// const exhibitionList = computed(() => searchStore.productList.exhibitionList)
</script>
<template></template>
