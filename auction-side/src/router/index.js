import { createRouter, createWebHistory } from 'vue-router'
import { PATH_NAME } from '../defined/const'
import Detail from '../views/ProductDetails.vue'

import { useAuthStore } from '../stores/auth'
import _TopPage from '@/components/top-page/_TopPage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  // eslint-disable-next-line no-unused-vars
  scrollBehavior(to, from, savedPosition) {
    // Scroll to the top position when entering a new route
    return { top: 0 }
  },
  routes: [
    {
      path: PATH_NAME.TOP,
      name: 'home',
      component: _TopPage,
      meta: {
        name: 'route.top',
        bodyId: 'home'
      }
    },
    {
      path: PATH_NAME.LOGIN,
      name: 'login',
      component: () => import('../views/Member/LoginPage.vue'),
      meta: {
        headerShow: true,
        footerShow: true,
        name: 'route.login',
        bodyId: 'login'
      }
    },
    {
      path: PATH_NAME.REMINDER,
      name: 'reminder',
      component: () => import('../views/Member/ReminderPage.vue'),
      meta: {
        name: 'route.reminder',
        bodyId: 'reminder'
      }
    },
    {
      path: PATH_NAME.ENTRY_INFO_REGIST,
      name: 'entryinfo-regist',
      component: () => import('../views/Member/RegisterPage.vue'),
      meta: {
        isRegist: true,
        name: 'route.register',
        bodyId: 'entry'
      }
    },
    {
      path: PATH_NAME.ENTRY_INFO_CONFIRM,
      name: 'entryinfo-complete',
      component: () => import('../views/Member/RegisterPage.vue'),
      meta: {
        isConfirm: true,
        name: 'route.register',
        bodyId: 'entry'
      }
    },
    {
      path: PATH_NAME.NOTICE_LIST,
      name: 'list-notice',
      component: () => import('../views/List/NoticeList.vue'),
      meta: {
        name: 'route.noticeList',
        bodyId: 'news'
      }
    },
    {
      path: PATH_NAME.NOTICE_LIST_IMPORTANT,
      name: 'list-notice-important',
      component: () => import('../views/List/NoticeList.vue'),
      meta: {
        name: 'route.importantNoticeList',
        bodyId: 'news'
      }
    },
    {
      path: PATH_NAME.BID_HISTORY_ALL,
      name: 'bid-history-all',
      component: () => import('../views/List/BidHistoryAll.vue'),
      meta: {
        requireAuth: true,
        name: 'route.bidHistoryAll',
        bodyId: 'result'
      }
    },
    {
      path: `${PATH_NAME.DETAIL}/:manageNo`,
      name: 'details',
      component: Detail,
      meta: {
        requireAuth: true,
        name: 'route.details',
        bodyClass: 'stock',
        bodyId: 'detail'
      }
    },
    {
      path: `${PATH_NAME.DETAIL}/:exhibitionItemNo/contact`,
      name: 'details-contact',
      component: () => import('../views/contact/ContactUs.vue'),
      meta: {
        requireAuth: true,
        name: 'route.contact',
        bodyId: 'contact',
        mainClass: 'contact-used'
      }
    },
    {
      path: `${PATH_NAME.DETAIL}/:exhibitionItemNo/contact-confirm`,
      name: 'details-contact-confirm',
      component: () => import('../views/contact/ContactUs.vue'),
      meta: {
        requireAuth: true,
        name: 'route.contactConfirm',
        bodyId: 'detail',
        mainClass: 'contact-used'
      }
    },
    {
      path: `${PATH_NAME.NOTICE_LIST}/:noticeNo`,
      name: 'notice-details',
      component: () => import('../views/NoticePage.vue'),
      meta: {
        name: 'route.noticeDetails',
        bodyId: 'news'
      }
    },
    {
      path: `${PATH_NAME.DETAIL}`,
      name: 'details-makeshop',
      component: Detail,
      meta: {
        requireAuth: true,
        name: 'route.details',
        bodyClass: 'stock',
        bodyId: 'detail'
      }
    },
    {
      path: `${PATH_NAME.NOTICE_LIST_IMPORTANT}/:noticeNo`,
      name: 'notice-important-details',
      component: () => import('../views/NoticePage.vue'),
      meta: {
        name: 'route.importantNotice',
        bodyId: 'news'
      }
    },
    {
      path: PATH_NAME.FAVORITES,
      name: 'favorites',
      component: () => import('../views/Member/MyPageFavorites.vue'),
      meta: {
        requireAuth: true,
        name: 'route.favorites',
        bodyClass: 'mypage',
        label: 'favorite.title'
      }
    },
    {
      path: PATH_NAME.BIDS,
      name: 'bids',
      component: () => import('../views/Member/MyPageFavorites.vue'),
      meta: {
        requireAuth: true,
        name: 'route.bidOngoing',
        bodyClass: 'mypage',
        label: 'auction.bidOngoing'
      }
    },
    {
      path: PATH_NAME.BID_HISTORY,
      name: 'bid-history',
      component: () => import('../views/Member/BidHistory.vue'),
      meta: {
        requireAuth: true,
        name: 'route.bidHistory',
        bodyClass: 'mypage',
        label: 'auction.bidHistory'
      }
    },
    {
      path: PATH_NAME.MYPAGE,
      name: 'my-page-edit',
      component: () => import('../views/Member/MyPageAccount.vue'),
      meta: {
        requireAuth: true,
        isRegist: true,
        name: 'route.myPage',
        bodyId: '',
        bodyClass: 'mypage'
      }
    },
    {
      path: PATH_NAME.MYPAGE_EDIT_CONFIRM,
      name: 'my-page-confirm',
      component: () => import('../views/Member/MyPageAccount.vue'),
      meta: {
        requireAuth: true,
        isConfirm: true,
        name: 'route.myPageEditConfirm',
        bodyId: 'entry'
      }
    },
    {
      path: PATH_NAME.PROFILE,
      name: 'profile',
      component: () => import('../views/Guidance/CompanyProfile.vue'),
      meta: {
        name: 'route.companyOverview',
        bodyId: 'profile'
      }
    },
    {
      path: PATH_NAME.TERMS,
      name: 'terms',
      component: () => import('../views/Guidance/Terms.vue'),
      meta: {
        name: 'route.terms',
        bodyId: 'terms'
      }
    },
    {
      path: PATH_NAME.PRIVACY,
      name: 'privacy',
      component: () => import('../views/Guidance/PrivacyPage.vue'),
      meta: {
        name: 'route.privacy',
        bodyId: 'privacy'
      }
    },
    {
      path: PATH_NAME.CONTACT,
      name: 'contact',
      component: () => import('../views/contact/ContactUs.vue'),
      meta: {
        name: 'route.contact',
        bodyId: 'contact'
      }
    },
    {
      path: PATH_NAME.CONTACT_CONFIRM,
      name: 'contact-confirm',
      component: () => import('../views/contact/ContactUs.vue'),
      meta: {
        name: 'route.contactConfirm',
        bodyId: 'contact'
      }
    },
    {
      path: PATH_NAME.GUIDE,
      name: 'guide',
      component: () => import('../views/Guidance/GuidePage.vue'),
      meta: {
        name: 'route.firstTime',
        bodyId: 'guide'
      }
    },
    {
      path: PATH_NAME.ORDER_CONTRACT,
      name: 'order-contract',
      component: () => import('../views/Guidance/OrderContract.vue'),
      meta: {
        name: 'route.toshuho',
        bodyId: 'tokushoho'
      }
    }
    // {
    //   path: '/:pathMatch(.*)*',
    //   component: TopView
    // }
  ]
})

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  authStore.reloadCookies()

  if (!to.hash) {
    window.scrollTo({ top: 0 })
  }

  if (authStore.isAuthenticated && to.name === 'login') {
    return next({ path: from.path })
  }
  if (to?.meta.requireAuth && !authStore.isAuthenticated) {
    return next({ name: 'login' })
  }
  return next()
})

/**
 * S3へデプロイ後に動的にインポートされたモジュールが取得できなかった場合のエラーハンドリング。
 * onErrorフックを使用してエラーが発生した場合にルートパス（`/`）に遷移
 * 参照: https://github.com/vitejs/vite/issues/11804#issuecomment-1406182566
 */
router.onError((error) => {
  console.log('router error:', error)
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    console.warn(`${error.message}, force reload.`)
    // 開発環境では強制リロードを行わない（Viteの環境変数を使用）
    const isDevelopment = import.meta.env.DEV
    if (!isDevelopment) {
      window.location.href = '/'
    } else {
      console.warn('Skipping force reload in development environment')
    }
    return
  }
  console.error('Unhandled router error:', error)
})

export default router
