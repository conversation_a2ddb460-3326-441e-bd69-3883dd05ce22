import { createPinia } from 'pinia'
import { createApp } from 'vue'

import App from './App.vue'
import router from './router'

import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { useI18n } from 'vue-i18n'
import VueSocialSharing from 'vue-social-sharing'
import { createVuetify } from 'vuetify'
import { aliases, mdi } from 'vuetify/iconsets/mdi'
import { createVueI18nAdapter } from 'vuetify/locale/adapters/vue-i18n'
import i18n from './language'

// Vuetify style
import 'vuetify/styles'
// Import gmo-saas style
import '@/assets/gmo-saas.css'

// jQuery is now loaded from index.html, so it's available globally

const app = createApp(App)
const vuetify = createVuetify({
  locale: {
    adapter: createVueI18nAdapter({ i18n, useI18n })
  },
  icons: {
    defaultSet: 'mdi',
    aliases,
    sets: {
      mdi
    }
  }
})
const pinia = createPinia()

pinia.use(piniaPluginPersistedstate)
app.use(pinia)
app.use(router)
app.use(vuetify)
app.use(VueSocialSharing)

app.mount('#app')
