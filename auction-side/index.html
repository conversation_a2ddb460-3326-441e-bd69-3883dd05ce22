<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="format-detection" content="telephone=no" />
    <title>AUCTION │ クラウドECオークション</title>

    <meta name="description" content="" />

    <link rel="shortcut icon" href="/favicon/favicon.ico" type="image/vnd.microsoft.icon" />
    <link rel="icon" href="/favicon/favicon.ico" type="image/vnd.microsoft.icon" />
    <link rel="apple-touch-icon" href="/favicon/apple_touch_icon.png" />

    <meta property="og:type" content="website" />
    <meta property="og:url" content="./" />
    <meta property="og:image" content="/favicon/ogp.png" />
    <meta property="og:title" content="BRANDBANK | ブランドバンクオークション" />
    <meta property="og:description" />
    <meta property="og:locale" content="ja_JP" />
    <link
      href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Round|Material+Icons+Sharp|Material+Icons+Two+Tone"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100;300;400;500;700;900&display=swap"
      rel="stylesheet"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:wght@400;700&display=swap"
      rel="stylesheet"
    />

    <!-- Load all JavaScript files from /public/js folder -->

    <!-- 1. Core jQuery (load first) -->
    <script src="/js/jquery.min.js"></script>

    <!-- 2. jQuery plugins and utilities -->
    <script src="/js/jquery.matchHeight.js"></script>
    <script src="/js/picturefill.min.js"></script>
    <script src="/js/remodal.min.js"></script>
    <script src="/js/prev-next.js"></script>
    <script src="/js/商品一覧高さ合わせ.js"></script>

    <!-- 3. Gallery related scripts -->
    <script src="/js/gallery/slick.min.js"></script>
    <script src="/js/gallery/photoswipe.min.js"></script>
    <script src="/js/gallery/photoswipe-ui-default.js"></script>
    <script src="/js/gallery/ofi.js"></script>
    <!-- Don't load gallery scripts directly - we'll handle initialization manually -->
    <!-- <script src="/js/gallery/gallery.js"></script> -->

    <script>
      function waitForGalleryElements() {
        const sliderFor = document.querySelector('.slider-for')
        const sliderNav = document.querySelector('.slider-nav')

        if (sliderFor && sliderNav && typeof $ !== 'undefined' && $.fn.slick) {
          // Check if already initialized
          if (!$(sliderFor).hasClass('slick-initialized')) {
            console.log('🎯 Gallery elements found! Initializing sliders...')

            // Your exact code from gallery20211111.js
            $('.slider-for').slick({
              asNavFor: '.slider-nav',
              autoplay: false,
              arrows: true,
              infinite: true
            })

            $('.slider-nav').slick({
              slidesToShow: 7,
              slidesToScroll: 1,
              asNavFor: '.slider-for',
              focusOnSelect: true,
              responsive: [
                {
                  breakpoint: 767,
                  settings: {
                    slidesToShow: 4
                  }
                }
              ]
            })

            console.log('✅ Gallery initialized successfully!')
          }
        } else {
          // Elements not ready yet, try again in 100ms
          console.log('⏳ Gallery elements not ready, retrying...')
          setTimeout(waitForGalleryElements, 100)
        }
      }

      // Start checking when DOM is ready
      $(document).ready(function () {
        console.log('📋 DOM ready, waiting for Vue gallery elements...')
        waitForGalleryElements()

        // Also check again after Vue route changes
        setTimeout(waitForGalleryElements, 500)
        setTimeout(waitForGalleryElements, 1000)
      })
    </script>

    <!-- 4. Common functionality -->
    <script src="/js/common.js"></script>
    <!-- Alternative common version (uncomment if needed) -->
    <!-- <script src="/js/common copy.js"></script> -->

    <!-- 5. Page-specific scripts -->
    <script src="/js/login.js"></script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
