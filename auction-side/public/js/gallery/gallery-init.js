// Gallery initialization script
// You can paste your existing client jQuery/Slick code here

(function() {
  'use strict';
  
  // Function to initialize gallery when elements are available
  function initGallery() {
    const mainSlider = document.querySelector('.slider-for');
    const thumbSlider = document.querySelector('.slider-nav');
    
    if (mainSlider && thumbSlider && typeof $ !== 'undefined' && $.fn.slick) {
      // Check if already initialized to avoid double initialization
      if (!$(mainSlider).hasClass('slick-initialized')) {
        
        // ========================================
        // PASTE YOUR EXISTING CLIENT CODE HERE
        // ========================================
        
        // Main slider initialization
        $(mainSlider).slick({
          slidesToShow: 1,
          slidesToScroll: 1,
          arrows: true,
          fade: true,
          asNavFor: '.slider-nav',
          responsive: [
            {
              breakpoint: 768,
              settings: {
                arrows: false,
                dots: true
              }
            }
          ]
        });
      }
      
      if (!$(thumbSlider).hasClass('slick-initialized')) {
        // Thumbnail slider initialization
        $(thumbSlider).slick({
          slidesToShow: 7,
          slidesToScroll: 1,
          asNavFor: '.slider-for',
          dots: false,
          arrows: true,
          centerMode: false,
          focusOnSelect: true,
          responsive: [
            {
              breakpoint: 768,
              settings: {
                slidesToShow: 4,
                arrows: false
              }
            },
            {
              breakpoint: 480,
              settings: {
                slidesToShow: 3,
                arrows: false
              }
            }
          ]
        });
      }
      
      // ========================================
      // ADD YOUR PHOTOSWIPE CODE HERE IF NEEDED
      // ========================================
      
      console.log('✅ Gallery initialized via external script');
    } else {
      // Retry after a short delay if elements not ready
      setTimeout(initGallery, 100);
    }
  }
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initGallery);
  } else {
    initGallery();
  }
  
  // Also listen for Vue route changes
  if (window.addEventListener) {
    window.addEventListener('popstate', function() {
      setTimeout(initGallery, 200);
    });
  }
  
})();
